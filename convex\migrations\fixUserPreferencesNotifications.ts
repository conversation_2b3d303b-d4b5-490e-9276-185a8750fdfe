import { internalMutation, mutation } from '../_generated/server';

/**
 * Migration to fix userPreferences documents where notifications is a boolean instead of an object
 * This fixes the schema validation error where some documents have notifications: true instead of the expected object structure
 */
export const fixUserPreferencesNotifications = internalMutation({
	args: {},
	handler: async (ctx) => {
		console.log('Starting migration to fix userPreferences notifications...');

		// Get all userPreferences documents
		const allPreferences = await ctx.db.query('userPreferences').collect();

		let fixedCount = 0;
		let skippedCount = 0;

		for (const pref of allPreferences) {
			// Check if settings.notifications is a boolean (the problematic case)
			// Use type assertion to bypass TypeScript checking since we know the data might be inconsistent
			const notifications = pref.settings?.notifications as any;

			if (notifications === true || notifications === false) {
				console.log(
					`Fixing userPreferences document ${pref._id} - notifications was boolean: ${notifications}`
				);

				// Convert boolean to proper object structure with defaults
				const newSettings = {
					...pref.settings,
					notifications: {
						mentions: true, // Default values
						assignee: true,
						threadReply: true,
						directMessage: true,
						weeklyDigest: notifications === true ? true : false, // Preserve the original boolean intent
						weeklyDigestDay: 'monday' as const,
					},
				};

				await ctx.db.patch(pref._id, {
					settings: newSettings,
				});

				fixedCount++;
			} else if (notifications && typeof notifications === 'object') {
				// Already an object, check if it has all required fields with defaults
				const notificationsObj = notifications as any;
				const needsUpdate =
					notificationsObj.mentions === undefined ||
					notificationsObj.assignee === undefined ||
					notificationsObj.threadReply === undefined ||
					notificationsObj.directMessage === undefined ||
					notificationsObj.weeklyDigest === undefined ||
					notificationsObj.weeklyDigestDay === undefined;

				if (needsUpdate) {
					console.log(
						`Updating userPreferences document ${pref._id} - adding missing notification fields`
					);

					const updatedNotifications = {
						mentions: notificationsObj.mentions ?? true,
						assignee: notificationsObj.assignee ?? true,
						threadReply: notificationsObj.threadReply ?? true,
						directMessage: notificationsObj.directMessage ?? true,
						weeklyDigest: notificationsObj.weeklyDigest ?? false,
						weeklyDigestDay:
							notificationsObj.weeklyDigestDay ?? ('monday' as const),
					};

					await ctx.db.patch(pref._id, {
						settings: {
							...pref.settings,
							notifications: updatedNotifications,
						},
					});

					fixedCount++;
				} else {
					skippedCount++;
				}
			} else {
				// No notifications field or it's null/undefined
				if (pref.settings) {
					console.log(
						`Adding notifications object to userPreferences document ${pref._id}`
					);

					await ctx.db.patch(pref._id, {
						settings: {
							...pref.settings,
							notifications: {
								mentions: true,
								assignee: true,
								threadReply: true,
								directMessage: true,
								weeklyDigest: false,
								weeklyDigestDay: 'monday' as const,
							},
						},
					});

					fixedCount++;
				} else {
					skippedCount++;
				}
			}
		}

		console.log(
			`Migration completed. Fixed: ${fixedCount}, Skipped: ${skippedCount}, Total: ${allPreferences.length}`
		);

		return {
			success: true,
			totalDocuments: allPreferences.length,
			fixedCount,
			skippedCount,
			message: `Migration completed successfully. Fixed ${fixedCount} documents, skipped ${skippedCount} documents.`,
		};
	},
});

/**
 * Public mutation to run the migration (for testing purposes)
 */
export const runMigration = mutation({
	args: {},
	handler: async (ctx) => {
		// Manually call the migration logic since we can't call internal mutations from public mutations
		console.log('Starting migration to fix userPreferences notifications...');

		// Get all userPreferences documents
		const allPreferences = await ctx.db.query('userPreferences').collect();

		let fixedCount = 0;
		let skippedCount = 0;

		for (const pref of allPreferences) {
			// Check if settings.notifications is a boolean (the problematic case)
			// Use type assertion to bypass TypeScript checking since we know the data might be inconsistent
			const notifications = pref.settings?.notifications as any;

			if (notifications === true || notifications === false) {
				console.log(
					`Fixing userPreferences document ${pref._id} - notifications was boolean: ${notifications}`
				);

				// Convert boolean to proper object structure with defaults
				const newSettings = {
					...pref.settings,
					notifications: {
						mentions: true, // Default values
						assignee: true,
						threadReply: true,
						directMessage: true,
						weeklyDigest: notifications === true ? true : false, // Preserve the original boolean intent
						weeklyDigestDay: 'monday' as const,
					},
				};

				await ctx.db.patch(pref._id, {
					settings: newSettings,
				});

				fixedCount++;
			} else {
				skippedCount++;
			}
		}

		console.log(
			`Migration completed. Fixed: ${fixedCount}, Skipped: ${skippedCount}, Total: ${allPreferences.length}`
		);

		return {
			success: true,
			totalDocuments: allPreferences.length,
			fixedCount,
			skippedCount,
			message: `Migration completed successfully. Fixed ${fixedCount} documents, skipped ${skippedCount} documents.`,
		};
	},
});

/**
 * Check current state of userPreferences documents
 */
export const checkUserPreferencesState = mutation({
	args: {},
	handler: async (ctx) => {
		// Get all userPreferences documents to check the current state
		const allPreferences = await ctx.db.query('userPreferences').collect();

		console.log(`Found ${allPreferences.length} userPreferences documents`);

		let problematicDocs = [];
		let validDocs = 0;

		for (const pref of allPreferences) {
			const notifications = pref.settings?.notifications as any;

			if (notifications === true || notifications === false) {
				problematicDocs.push({
					id: pref._id,
					userId: pref.userId,
					notificationsValue: notifications,
				});
			} else if (notifications && typeof notifications === 'object') {
				validDocs++;
			}
		}

		console.log(
			`Found ${problematicDocs.length} documents with boolean notifications`
		);
		console.log(`Found ${validDocs} documents with valid object notifications`);

		return {
			totalDocuments: allPreferences.length,
			problematicDocuments: problematicDocs.length,
			validDocuments: validDocs,
			problematicDocs: problematicDocs.slice(0, 5), // Show first 5 for inspection
			needsMigration: problematicDocs.length > 0,
		};
	},
});
